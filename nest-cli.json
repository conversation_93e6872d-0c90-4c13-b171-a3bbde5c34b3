{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/nestapi/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/nestapi/tsconfig.app.json"}, "monorepo": true, "root": "apps/nestapi", "projects": {"nestapi": {"type": "application", "root": "apps/nestapi", "entryFile": "main", "sourceRoot": "apps/nestapi/src", "compilerOptions": {"tsConfigPath": "apps/nestapi/tsconfig.app.json"}}}}