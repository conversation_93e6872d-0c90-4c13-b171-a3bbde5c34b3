import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserService } from './user.service';
import { User } from './entities/user.entity';

describe('UserService', () => {
  let service: UserService;
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'mysql',
          host: 'localhost',
          port: 3306,
          username: 'root',
          password: 'root',
          database: 'nestapi',
          entities: [User],
          synchronize: true,
        }),
        TypeOrmModule.forFeature([User]),
      ],
      providers: [UserService],
    }).compile();

    service = module.get<UserService>(UserService);
  });

  afterAll(async () => {
    await module.close();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('CRUD operations', () => {
    let testid = 0;
    it('should create a new user', async () => {
      const userData = {
        name: '<PERSON>',
        email: 'john.doe@com',
        age: 30,
        address: '123 Main St',
        token: 'some-secret-token',
      };
      const newUser = await service.create(userData);
      testid = newUser.id;
      expect(newUser).toBeDefined();
      expect(newUser.name).toEqual(userData.name);
      expect(newUser.email).toEqual(userData.email);
      expect(newUser.age).toEqual(userData.age);
      expect(newUser.address).toEqual(userData.address);
      expect(newUser.token).toEqual(userData.token);
    });

    it('should find all users', async () => {
      const users = await service.findAll();
      expect(users).toBeInstanceOf(Array);
      expect(users.length).toBeGreaterThan(0);
    });

    it('should find one user by id', async () => {
      const user = await service.findOne(testid);
      expect(user).not.toBeNull();
      if (user) {
        expect(user.id).toEqual(testid);
        expect(user.name).toEqual('John Doe');
        expect(user.email).toEqual('john.doe@com');
      }
    });

    it('should update a user', async () => {
      const newName = 'Jane Doe';
      const newAge = 25;
      const updatedUser = await service.update(testid, {
        name: newName,
        age: newAge,
      });
      expect(updatedUser).toBeDefined();
      if (updatedUser) {
        expect(updatedUser.name).toEqual(newName);
        expect(updatedUser.age).toEqual(newAge);
      }
    });

    it('should remove a user', async () => {
      await service.remove(testid);
      const user = await service.findOne(testid);
      expect(user).toBeNull();
    });
  });
});
