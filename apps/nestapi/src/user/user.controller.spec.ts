import { Test, TestingModule } from '@nestjs/testing';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';

const mockUserService = {
  create: jest.fn(),
  findAll: jest.fn(),
  findOne: jest.fn(),
  update: jest.fn(),
  remove: jest.fn(),
};

describe('UserController', () => {
  let controller: UserController;
  let userService: UserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        {
          provide: UserService,
          useValue: mockUserService,
        },
      ],
    }).compile();

    controller = module.get<UserController>(UserController);
    userService = module.get<UserService>(UserService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should call userService.create and return the created user', async () => {
      const createUserDto: CreateUserDto = {
        name: 'Test User',
        email: '<EMAIL>',
        age: 25,
        address: '123 Test St',
        token: 'test-token',
      };
      const expectedUser: User = {
        id: 1,
        ...createUserDto,
      };

      mockUserService.create.mockResolvedValue(expectedUser);

      const result = await controller.create(createUserDto);

      expect(mockUserService.create).toHaveBeenCalledWith(createUserDto);
      expect(result).toEqual(expectedUser);
    });

    it('should handle service errors', async () => {
      const createUserDto: CreateUserDto = {
        name: 'Test User',
        email: '<EMAIL>',
        age: 25,
        address: '123 Test St',
        token: 'test-token',
      };

      mockUserService.create.mockRejectedValue(new Error('Database error'));

      await expect(controller.create(createUserDto)).rejects.toThrow(
        'Database error',
      );
    });

    it('should create user with valid email format', async () => {
      const createUserDto: CreateUserDto = {
        name: 'Test User',
        email: '<EMAIL>',
        age: 25,
        address: '123 Test St',
        token: 'test-token',
      };
      const expectedUser: User = {
        id: 1,
        ...createUserDto,
      };

      mockUserService.create.mockResolvedValue(expectedUser);

      const result = await controller.create(createUserDto);

      expect(mockUserService.create).toHaveBeenCalledWith(createUserDto);
      expect(result).toEqual(expectedUser);
    });
  });

  describe('findAll', () => {
    it('should call userService.findAll and return all users', async () => {
      const expectedUsers: User[] = [
        {
          id: 1,
          name: 'User 1',
          email: '<EMAIL>',
          age: 25,
          address: '123 Test St',
          token: 'token1',
        },
        {
          id: 2,
          name: 'User 2',
          email: '<EMAIL>',
          age: 30,
          address: '456 Test Ave',
          token: 'token2',
        },
      ];

      mockUserService.findAll.mockResolvedValue(expectedUsers);

      const result = await controller.findAll();

      expect(mockUserService.findAll).toHaveBeenCalled();
      expect(result).toEqual(expectedUsers);
    });
  });

  describe('findOne', () => {
    it('should call userService.findOne with correct id and return the user', async () => {
      const userId = '1';
      const expectedUser: User = {
        id: 1,
        name: 'Test User',
        email: '<EMAIL>',
        age: 25,
        address: '123 Test St',
        token: 'test-token',
      };

      mockUserService.findOne.mockResolvedValue(expectedUser);

      const result = await controller.findOne(userId);

      expect(mockUserService.findOne).toHaveBeenCalledWith(1);
      expect(result).toEqual(expectedUser);
    });

    it('should handle non-existent user', async () => {
      const userId = '999';

      mockUserService.findOne.mockResolvedValue(null);

      const result = await controller.findOne(userId);

      expect(mockUserService.findOne).toHaveBeenCalledWith(999);
      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    it('should call userService.update with correct parameters and return updated user', async () => {
      const userId = '1';
      const updateUserDto: UpdateUserDto = {
        name: 'Updated User',
        age: 26,
      };
      const expectedUser: User = {
        id: 1,
        name: 'Updated User',
        email: '<EMAIL>',
        age: 26,
        address: '123 Test St',
        token: 'test-token',
      };

      mockUserService.update.mockResolvedValue(expectedUser);

      const result = await controller.update(userId, updateUserDto);

      expect(mockUserService.update).toHaveBeenCalledWith(1, updateUserDto);
      expect(result).toEqual(expectedUser);
    });

    it('should handle update of non-existent user', async () => {
      const userId = '999';
      const updateUserDto: UpdateUserDto = { name: 'Updated User' };

      mockUserService.update.mockResolvedValue(null);

      const result = await controller.update(userId, updateUserDto);

      expect(mockUserService.update).toHaveBeenCalledWith(999, updateUserDto);
      expect(result).toBeNull();
    });
  });

  describe('remove', () => {
    it('should call userService.remove with correct id', async () => {
      const userId = '1';

      mockUserService.remove.mockResolvedValue(undefined);

      const result = await controller.remove(userId);

      expect(mockUserService.remove).toHaveBeenCalledWith(1);
      expect(result).toBeUndefined();
    });

    it('should handle removal errors', async () => {
      const userId = '1';

      mockUserService.remove.mockRejectedValue(new Error('Delete failed'));

      await expect(controller.remove(userId)).rejects.toThrow('Delete failed');
    });
  });
});
