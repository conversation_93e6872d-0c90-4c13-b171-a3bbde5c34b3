import { validate } from 'class-validator';
import { CreateUserDto } from './create-user.dto';

describe('CreateUserDto', () => {
  let createUserDto: CreateUserDto;

  beforeEach(() => {
    createUserDto = new CreateUserDto();
  });

  describe('email validation', () => {
    it('should pass validation with valid email', async () => {
      createUserDto.name = 'Test User';
      createUserDto.address = 'Test Address';
      createUserDto.email = '<EMAIL>';
      createUserDto.age = 25;
      createUserDto.token = 'test-token';

      const errors = await validate(createUserDto);
      expect(errors).toHaveLength(0);
    });

    it('should fail validation with invalid email format', async () => {
      createUserDto.name = 'Test User';
      createUserDto.address = 'Test Address';
      createUserDto.email = 'invalid-email';
      createUserDto.age = 25;
      createUserDto.token = 'test-token';

      const errors = await validate(createUserDto);
      expect(errors).toHaveLength(1);
      expect(errors[0]?.property).toBe('email');
      expect(errors[0]?.constraints).toHaveProperty('isEmail');
      expect(errors[0]?.constraints?.isEmail).toBe('email格式不正确');
    });

    it('should fail validation with missing @ symbol', async () => {
      createUserDto.name = 'Test User';
      createUserDto.address = 'Test Address';
      createUserDto.email = 'testexample.com';
      createUserDto.age = 25;
      createUserDto.token = 'test-token';

      const errors = await validate(createUserDto);
      expect(errors).toHaveLength(1);
      expect(errors[0]?.property).toBe('email');
      expect(errors[0]?.constraints).toHaveProperty('isEmail');
    });

    it('should fail validation with missing domain', async () => {
      createUserDto.name = 'Test User';
      createUserDto.address = 'Test Address';
      createUserDto.email = 'test@';
      createUserDto.age = 25;
      createUserDto.token = 'test-token';

      const errors = await validate(createUserDto);
      expect(errors).toHaveLength(1);
      expect(errors[0]?.property).toBe('email');
      expect(errors[0]?.constraints).toHaveProperty('isEmail');
    });

    it('should fail validation with empty email', async () => {
      createUserDto.name = 'Test User';
      createUserDto.address = 'Test Address';
      createUserDto.email = '';
      createUserDto.age = 25;
      createUserDto.token = 'test-token';

      const errors = await validate(createUserDto);
      expect(errors).toHaveLength(1);
      expect(errors[0]?.property).toBe('email');
      expect(errors[0]?.constraints).toHaveProperty('isEmail');
    });

    it('should pass validation with various valid email formats', async () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      for (const email of validEmails) {
        createUserDto.name = 'Test User';
        createUserDto.address = 'Test Address';
        createUserDto.email = email;
        createUserDto.age = 25;
        createUserDto.token = 'test-token';

        const errors = await validate(createUserDto);
        expect(errors).toHaveLength(0);
      }
    });
  });

  describe('other field validations', () => {
    it('should fail validation with non-string name', async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      createUserDto.name = 123 as any;
      createUserDto.address = 'Test Address';
      createUserDto.email = '<EMAIL>';
      createUserDto.age = 25;
      createUserDto.token = 'test-token';

      const errors = await validate(createUserDto);
      expect(errors).toHaveLength(1);
      expect(errors[0]?.property).toBe('name');
      expect(errors[0]?.constraints).toHaveProperty('isString');
    });

    it('should fail validation with non-string address', async () => {
      createUserDto.name = 'Test User';
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      createUserDto.address = 123 as any;
      createUserDto.email = '<EMAIL>';
      createUserDto.age = 25;
      createUserDto.token = 'test-token';

      const errors = await validate(createUserDto);
      expect(errors).toHaveLength(1);
      expect(errors[0]?.property).toBe('address');
      expect(errors[0]?.constraints).toHaveProperty('isString');
    });

    it('should fail validation with non-number age', async () => {
      createUserDto.name = 'Test User';
      createUserDto.address = 'Test Address';
      createUserDto.email = '<EMAIL>';
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      createUserDto.age = 'twenty-five' as any;
      createUserDto.token = 'test-token';

      const errors = await validate(createUserDto);
      expect(errors).toHaveLength(1);
      expect(errors[0]?.property).toBe('age');
      expect(errors[0]?.constraints).toHaveProperty('isNumber');
    });

    it('should fail validation with non-string token', async () => {
      createUserDto.name = 'Test User';
      createUserDto.address = 'Test Address';
      createUserDto.email = '<EMAIL>';
      createUserDto.age = 25;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      createUserDto.token = 123 as any;

      const errors = await validate(createUserDto);
      expect(errors).toHaveLength(1);
      expect(errors[0]?.property).toBe('token');
      expect(errors[0]?.constraints).toHaveProperty('isString');
    });
  });
});
