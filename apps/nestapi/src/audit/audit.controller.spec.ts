import { Test, TestingModule } from '@nestjs/testing';
import { AuditController } from './audit.controller';
import { AuditService } from './audit.service';
import { CreateAuditDto } from './dto/create-audit.dto';
import { UpdateAuditDto } from './dto/update-audit.dto';

describe('AuditController', () => {
  let controller: AuditController;
  let service: AuditService;

  const mockAuditService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuditController],
      providers: [
        {
          provide: AuditService,
          useValue: mockAuditService,
        },
      ],
    }).compile();

    controller = module.get<AuditController>(AuditController);
    service = module.get<AuditService>(AuditService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create an audit record', async () => {
      const createAuditDto: CreateAuditDto = {
        title: 'Test Audit',
        content: 'Test content',
        opTime: new Date(),
        isAproved: false,
      };
      const result = {
        id: 1,
        ...createAuditDto,
        createdTime: new Date(),
        updateTime: new Date(),
      };

      mockAuditService.create.mockResolvedValue(result);

      expect(await controller.create(createAuditDto)).toBe(result);
      expect(service.create).toHaveBeenCalledWith(createAuditDto);
    });
  });

  describe('findAll', () => {
    it('should return an array of audit records', async () => {
      const result = [
        {
          id: 1,
          title: 'Test Audit',
          content: 'Test content',
          opTime: new Date(),
          isAproved: false,
          createdTime: new Date(),
          updateTime: new Date(),
        },
      ];

      mockAuditService.findAll.mockResolvedValue(result);

      expect(await controller.findAll()).toBe(result);
      expect(service.findAll).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a single audit record', async () => {
      const result = {
        id: 1,
        title: 'Test Audit',
        content: 'Test content',
        opTime: new Date(),
        isAproved: false,
        createdTime: new Date(),
        updateTime: new Date(),
      };

      mockAuditService.findOne.mockResolvedValue(result);

      expect(await controller.findOne('1')).toBe(result);
      expect(service.findOne).toHaveBeenCalledWith(1);
    });
  });

  describe('update', () => {
    it('should update an audit record', async () => {
      const updateAuditDto: UpdateAuditDto = {
        title: 'Updated Audit',
        isAproved: true,
      };
      const result = {
        id: 1,
        title: 'Updated Audit',
        content: 'Test content',
        opTime: new Date(),
        isAproved: true,
        createdTime: new Date(),
        updateTime: new Date(),
      };

      mockAuditService.update.mockResolvedValue(result);

      expect(await controller.update('1', updateAuditDto)).toBe(result);
      expect(service.update).toHaveBeenCalledWith(1, updateAuditDto);
    });
  });

  describe('remove', () => {
    it('should remove an audit record', async () => {
      mockAuditService.remove.mockResolvedValue(undefined);

      expect(await controller.remove('1')).toBeUndefined();
      expect(service.remove).toHaveBeenCalledWith(1);
    });
  });
});