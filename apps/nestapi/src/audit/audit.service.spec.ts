import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuditService } from './audit.service';
import { Audit } from './entities/audit.entity';

describe('AuditService', () => {
  let service: AuditService;
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'mysql',
          host: 'localhost',
          port: 3306,
          username: 'root',
          password: 'root',
          database: 'nestapi',
          entities: [Audit],
          synchronize: true,
        }),
        TypeOrmModule.forFeature([Audit]),
      ],
      providers: [AuditService],
    }).compile();

    service = module.get<AuditService>(AuditService);
  });

  afterAll(async () => {
    await module.close();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('CRUD operations', () => {
    let testid = 0;
    it('should create a new audit record', async () => {
      const auditData = {
        title: 'Test Audit',
        content: 'This is a test audit content',
        opTime: new Date(),
        isAproved: false,
      };
      const newAudit = await service.create(auditData);
      testid = newAudit.id;
      expect(newAudit).toBeDefined();
      expect(newAudit.title).toEqual(auditData.title);
      expect(newAudit.content).toEqual(auditData.content);
      expect(newAudit.isAproved).toBe(false);
      expect(newAudit.createdTime).toBeDefined();
      expect(newAudit.updateTime).toBeDefined();
    });

    it('should find all audit records', async () => {
      const auditRecords = await service.findAll();
      expect(auditRecords).toBeInstanceOf(Array);
      expect(auditRecords.length).toBeGreaterThan(0);
    });

    it('should find one audit record by id', async () => {
      const auditRecord = await service.findOne(testid);
      expect(auditRecord).not.toBeNull();
      if (auditRecord) {
        expect(auditRecord.id).toEqual(testid);
        expect(auditRecord.title).toEqual('Test Audit');
        expect(auditRecord.content).toEqual('This is a test audit content');
      }
    });

    it('should update an audit record', async () => {
      const updatedTitle = 'Updated Audit Title';
      const updatedAudit = await service.update(testid, { 
        title: updatedTitle,
        isAproved: true,
      });
      expect(updatedAudit).toBeDefined();
      if (updatedAudit) {
        expect(updatedAudit.title).toEqual(updatedTitle);
        expect(updatedAudit.isAproved).toBe(true);
      }
    });

    it('should remove an audit record', async () => {
      await service.remove(testid);
      const auditRecord = await service.findOne(testid);
      expect(auditRecord).toBeNull();
    });
  });
});