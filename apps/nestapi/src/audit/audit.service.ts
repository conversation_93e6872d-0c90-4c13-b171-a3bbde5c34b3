import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateAuditDto } from './dto/create-audit.dto';
import { UpdateAuditDto } from './dto/update-audit.dto';
import { Audit } from './entities/audit.entity';

@Injectable()
export class AuditService {
  constructor(
    @InjectRepository(Audit)
    private auditRepository: Repository<Audit>,
  ) {}

  create(createAuditDto: CreateAuditDto): Promise<Audit> {
    const audit = this.auditRepository.create(createAuditDto);
    return this.auditRepository.save(audit);
  }

  findAll(): Promise<Audit[]> {
    return this.auditRepository.find();
  }

  async findOne(id: number): Promise<Audit | null> {
    const audit = await this.auditRepository.findOneBy({ id });
    if (!audit) {
      return null;
    }
    return audit;
  }

  async update(id: number, updateAuditDto: UpdateAuditDto): Promise<Audit | null> {
    await this.auditRepository.update(id, updateAuditDto);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    await this.auditRepository.delete(id);
  }
}