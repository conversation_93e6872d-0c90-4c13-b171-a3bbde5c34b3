import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthService } from './auth.service';
import { Auth } from './entities/auth.entity';

describe('AuthService', () => {
  let service: AuthService;
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'mysql',
          host: 'localhost',
          port: 3306,
          username: 'root',
          password: 'root',
          database: 'nestapi',
          entities: [Auth],
          synchronize: true,
        }),
        TypeOrmModule.forFeature([Auth]),
      ],
      providers: [AuthService],
    }).compile();

    service = module.get<AuthService>(AuthService);
  });

  afterAll(async () => {
    await module.close();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('CRUD operations', () => {
    let testid = 0;
    it('should create a new auth record', async () => {
      const authData = { name: 'testuser', passwd: 'password' };
      const newAuth = await service.create(authData);
      testid = newAuth.id; // Store the created id for further tests
      expect(newAuth).toBeDefined();
      expect(newAuth.name).toEqual(authData.name);
      expect(newAuth.isActived).toBe(true); // Check default value
    });

    it('should find all auth records', async () => {
      const authRecords = await service.findAll();
      expect(authRecords).toBeInstanceOf(Array);
      expect(authRecords.length).toBeGreaterThan(0);
    });

    it('should find one auth record by id', async () => {
      const authRecord = await service.findOne(testid);
      expect(authRecord).not.toBeNull();
      if (authRecord) {
        expect(authRecord.id).toEqual(testid);
        expect(authRecord.name).toEqual('testuser');
      }
    });

    it('should update an auth record', async () => {
      const newName = 'updateduser';
      const updatedAuth = await service.update(testid, { name: newName });
      expect(updatedAuth).toBeDefined();
      if (updatedAuth) {
        expect(updatedAuth.name).toEqual(newName);
      }
    });

    it('should remove an auth record', async () => {
      await service.remove(testid);
      const authRecord = await service.findOne(testid);
      expect(authRecord).toBeNull();
    });
  });
});
