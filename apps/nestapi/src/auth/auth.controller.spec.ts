import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { CreateAuthDto } from './dto/create-auth.dto';
import { UpdateAuthDto } from './dto/update-auth.dto';
import { Auth } from './entities/auth.entity';

const mockAuthService = {
  create: jest.fn(),
  findAll: jest.fn(),
  findOne: jest.fn(),
  update: jest.fn(),
  remove: jest.fn(),
};

describe('AuthController', () => {
  let controller: AuthController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should call authService.create and return the created auth', async () => {
      const createAuthDto: CreateAuthDto = {
        name: 'testuser',
        passwd: 'testpassword',
        isActived: true,
      };
      const expectedAuth: Auth = {
        id: 1,
        name: 'testuser',
        passwd: 'testpassword',
        isActived: true,
      };

      mockAuthService.create.mockResolvedValue(expectedAuth);

      const result = await controller.create(createAuthDto);

      expect(mockAuthService.create).toHaveBeenCalledWith(createAuthDto);
      expect(result).toEqual(expectedAuth);
    });

    it('should handle service errors', async () => {
      const createAuthDto: CreateAuthDto = {
        name: 'testuser',
        passwd: 'testpassword',
      };

      mockAuthService.create.mockRejectedValue(new Error('Database error'));

      await expect(controller.create(createAuthDto)).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('findAll', () => {
    it('should call authService.findAll and return all auths', async () => {
      const expectedAuths: Auth[] = [
        {
          id: 1,
          name: 'user1',
          passwd: 'password1',
          isActived: true,
        },
        {
          id: 2,
          name: 'user2',
          passwd: 'password2',
          isActived: false,
        },
      ];

      mockAuthService.findAll.mockResolvedValue(expectedAuths);

      const result = await controller.findAll();

      expect(mockAuthService.findAll).toHaveBeenCalled();
      expect(result).toEqual(expectedAuths);
    });
  });

  describe('findOne', () => {
    it('should call authService.findOne with correct id and return the auth', async () => {
      const authId = '1';
      const expectedAuth: Auth = {
        id: 1,
        name: 'testuser',
        passwd: 'testpassword',
        isActived: true,
      };

      mockAuthService.findOne.mockResolvedValue(expectedAuth);

      const result = await controller.findOne(authId);

      expect(mockAuthService.findOne).toHaveBeenCalledWith(1);
      expect(result).toEqual(expectedAuth);
    });

    it('should handle non-existent auth', async () => {
      const authId = '999';

      mockAuthService.findOne.mockResolvedValue(null);

      const result = await controller.findOne(authId);

      expect(mockAuthService.findOne).toHaveBeenCalledWith(999);
      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    it('should call authService.update with correct parameters and return updated auth', async () => {
      const authId = '1';
      const updateAuthDto: UpdateAuthDto = {
        name: 'updateduser',
        isActived: false,
      };
      const expectedAuth: Auth = {
        id: 1,
        name: 'updateduser',
        passwd: 'testpassword',
        isActived: false,
      };

      mockAuthService.update.mockResolvedValue(expectedAuth);

      const result = await controller.update(authId, updateAuthDto);

      expect(mockAuthService.update).toHaveBeenCalledWith(1, updateAuthDto);
      expect(result).toEqual(expectedAuth);
    });

    it('should handle update of non-existent auth', async () => {
      const authId = '999';
      const updateAuthDto: UpdateAuthDto = { name: 'updateduser' };

      mockAuthService.update.mockResolvedValue(null);

      const result = await controller.update(authId, updateAuthDto);

      expect(mockAuthService.update).toHaveBeenCalledWith(999, updateAuthDto);
      expect(result).toBeNull();
    });
  });

  describe('remove', () => {
    it('should call authService.remove with correct id', async () => {
      const authId = '1';

      mockAuthService.remove.mockResolvedValue(undefined);

      const result = await controller.remove(authId);

      expect(mockAuthService.remove).toHaveBeenCalledWith(1);
      expect(result).toBeUndefined();
    });

    it('should handle removal errors', async () => {
      const authId = '1';

      mockAuthService.remove.mockRejectedValue(new Error('Delete failed'));

      await expect(controller.remove(authId)).rejects.toThrow('Delete failed');
    });
  });
});
