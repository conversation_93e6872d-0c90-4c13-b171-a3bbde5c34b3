import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateAuthDto } from './dto/create-auth.dto';
import { UpdateAuthDto } from './dto/update-auth.dto';
import { Auth } from './entities/auth.entity';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(Auth)
    private authRepository: Repository<Auth>,
  ) {}

  create(createAuthDto: CreateAuthDto): Promise<Auth> {
    const auth = this.authRepository.create(createAuthDto);
    return this.authRepository.save(auth);
  }

  findAll(): Promise<Auth[]> {
    return this.authRepository.find();
  }

  async findOne(id: number): Promise<Auth | null> {
    const auth = await this.authRepository.findOneBy({ id });
    if (!auth) {
      return null; // Handle not found case
    }
    return auth;
  }

  async update(id: number, updateAuthDto: UpdateAuthDto): Promise<Auth | null> {
    await this.authRepository.update(id, updateAuthDto);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    await this.authRepository.delete(id);
  }
}
